{"id": "professional-insight-form", "fields": [{"id": "date", "label": "洞察日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "专业洞察记录日期"}, {"id": "insightTitle", "label": "洞察标题", "type": "text", "description": "用一句话概括这个专业洞察"}, {"id": "insightSource", "label": "洞察来源", "type": "select", "options": [{"id": "client-consultation", "label": "客户咨询", "value": "客户咨询"}, {"id": "case-analysis", "label": "案例分析", "value": "案例分析"}, {"id": "industry-research", "label": "行业研究", "value": "行业研究"}, {"id": "regulatory-study", "label": "法规研究", "value": "法规研究"}, {"id": "peer-discussion", "label": "同行讨论", "value": "同行讨论"}, {"id": "practical-experience", "label": "实践经验", "value": "实践经验"}, {"id": "cross-industry", "label": "跨行业观察", "value": "跨行业观察"}], "description": "选择洞察的来源"}, {"id": "insightLevel", "label": "洞察层次", "type": "select", "options": [{"id": "surface-observation", "label": "表面观察", "value": "表面观察"}, {"id": "pattern-recognition", "label": "模式识别", "value": "模式识别"}, {"id": "principle-discovery", "label": "原理发现", "value": "原理发现"}, {"id": "system-thinking", "label": "系统思考", "value": "系统思考"}, {"id": "paradigm-shift", "label": "范式转换", "value": "范式转换"}], "description": "选择洞察的深度层次"}, {"id": "originalProblem", "label": "原始问题", "type": "textarea", "rows": 3, "description": "最初遇到的问题或困惑"}, {"id": "conventionalWisdom", "label": "传统认知", "type": "textarea", "rows": 2, "description": "行业内的传统认知或常规做法"}, {"id": "newInsight", "label": "新洞察", "type": "textarea", "rows": 3, "description": "你发现的新洞察或不同视角"}, {"id": "evidenceSupport", "label": "证据支撑", "type": "textarea", "rows": 3, "description": "支撑这个洞察的具体证据或案例"}, {"id": "contradictoryEvidence", "label": "反驳证据", "type": "textarea", "rows": 2, "description": "可能反驳这个洞察的证据或观点"}, {"id": "applicabilityScope", "label": "适用范围", "type": "textarea", "rows": 2, "description": "这个洞察适用的范围和边界条件"}, {"id": "practicalImplication", "label": "实践意义", "type": "textarea", "rows": 3, "description": "这个洞察对实际工作的指导意义"}, {"id": "businessValue", "label": "商业价值", "type": "select", "options": [{"id": "cost-reduction", "label": "成本降低", "value": "成本降低"}, {"id": "efficiency-improvement", "label": "效率提升", "value": "效率提升"}, {"id": "risk-mitigation", "label": "风险缓解", "value": "风险缓解"}, {"id": "opportunity-identification", "label": "机会识别", "value": "机会识别"}, {"id": "competitive-advantage", "label": "竞争优势", "value": "竞争优势"}, {"id": "innovation-direction", "label": "创新方向", "value": "创新方向"}, {"id": "strategic-guidance", "label": "战略指导", "value": "战略指导"}], "description": "这个洞察的主要商业价值"}, {"id": "verificationMethod", "label": "验证方法", "type": "textarea", "rows": 2, "description": "如何验证这个洞察的正确性"}, {"id": "relatedConcepts", "label": "关联概念", "type": "text", "description": "与这个洞察相关的其他概念或理论"}, {"id": "future<PERSON>ese<PERSON><PERSON>", "label": "后续研究方向", "type": "textarea", "rows": 2, "description": "基于这个洞察可以深入研究的方向"}, {"id": "communicationStrategy", "label": "传播策略", "type": "select", "options": [{"id": "academic-paper", "label": "学术论文", "value": "学术论文"}, {"id": "industry-article", "label": "行业文章", "value": "行业文章"}, {"id": "client-presentation", "label": "客户演示", "value": "客户演示"}, {"id": "conference-speech", "label": "会议演讲", "value": "会议演讲"}, {"id": "social-media", "label": "社交媒体", "value": "社交媒体"}, {"id": "internal-training", "label": "内部培训", "value": "内部培训"}, {"id": "book-chapter", "label": "书籍章节", "value": "书籍章节"}], "description": "最适合的传播方式"}, {"id": "confidentialityLevel", "label": "保密级别", "type": "select", "options": [{"id": "public", "label": "公开", "value": "公开"}, {"id": "industry-sharing", "label": "行业分享", "value": "行业分享"}, {"id": "client-only", "label": "仅限客户", "value": "仅限客户"}, {"id": "internal-only", "label": "内部专用", "value": "内部专用"}, {"id": "confidential", "label": "机密", "value": "机密"}], "description": "这个洞察的保密级别"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 专业洞察提炼表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const insightTitle = form.insightTitle || '专业洞察记录';\n    const insightSource = form.insightSource || '客户咨询';\n    const insightLevel = form.insightLevel || '模式识别';\n    const originalProblem = form.originalProblem || '原始问题';\n    const conventionalWisdom = form.conventionalWisdom || '传统认知';\n    const newInsight = form.newInsight || '新洞察';\n    const evidenceSupport = form.evidenceSupport || '证据支撑';\n    const contradictoryEvidence = form.contradictoryEvidence || '反驳证据';\n    const applicabilityScope = form.applicabilityScope || '适用范围';\n    const practicalImplication = form.practicalImplication || '实践意义';\n    const businessValue = form.businessValue || '效率提升';\n    const verificationMethod = form.verificationMethod || '验证方法';\n    const relatedConcepts = form.relatedConcepts || '关联概念';\n    const futureResearch = form.futureResearch || '后续研究方向';\n    const communicationStrategy = form.communicationStrategy || '行业文章';\n    const confidentialityLevel = form.confidentialityLevel || '行业分享';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\ntitle: ${insightTitle}\\ninsightSource: ${insightSource}\\ninsightLevel: ${insightLevel}\\nbusinessValue: ${businessValue}\\ncommunicationStrategy: ${communicationStrategy}\\nconfidentialityLevel: ${confidentialityLevel}\\ntags:\\n  - 专业洞察提炼\\n  - ${insightSource}\\n  - ${insightLevel}\\n  - ${businessValue}\\n  - ${communicationStrategy}\\ncreatedBy: 专业洞察提炼系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# ${insightTitle}\\n\\n## 📋 洞察基础信息\\n\\n**洞察日期：** ${date}\\n**洞察来源：** ${insightSource}\\n**洞察层次：** ${insightLevel}\\n**商业价值：** ${businessValue}\\n**保密级别：** ${confidentialityLevel}\\n\\n## 🎯 问题与认知\\n\\n### 原始问题\\n${originalProblem}\\n\\n### 传统认知\\n${conventionalWisdom}\\n\\n### 新洞察\\n${newInsight}\\n\\n## 🔍 证据分析\\n\\n### 支撑证据\\n${evidenceSupport}\\n\\n### 反驳证据\\n${contradictoryEvidence}\\n\\n### 适用范围\\n${applicabilityScope}\\n\\n## 💡 价值与应用\\n\\n### 实践意义\\n${practicalImplication}\\n\\n### 验证方法\\n${verificationMethod}\\n\\n### 关联概念\\n${relatedConcepts}\\n\\n## 🚀 发展方向\\n\\n### 后续研究方向\\n${futureResearch}\\n\\n### 传播策略\\n**最佳传播方式：** ${communicationStrategy}\\n\\n---\\n\\n**快速标签：** #${insightSource} #${insightLevel} #${businessValue}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的专业洞察分析师。请基于以下专业洞察记录，提供深度的分析和拓展：\\n\\n洞察记录：\\n${baseTemplate}\\n\\n请从以下角度进行深度分析：\\n1. 洞察的理论基础和学术价值\\n2. 与现有理论体系的关系\\n3. 跨领域的应用可能性\\n4. 潜在的商业化路径\\n5. 可能的挑战和局限性\\n6. 进一步验证和完善的建议\\n\\n要求语言严谨专业，体现深度思考和前瞻性视野。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.7,\n          max_tokens: 2500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI深度分析暂时不可用，请手动补充专业洞察分析)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## 🧠 AI深度洞察分析\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 洞察验证记录\\n\\n<!-- 记录洞察的验证过程和结果 -->\\n\\n## 🔄 洞察迭代完善\\n\\n<!-- 记录洞察的进一步完善和发展 -->\\n\\n---\\n\\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI洞察分析：DeepSeek | 专业洞察库*`;\n    \n    const fileName = `专业洞察-${insightLevel}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/专业洞察提炼/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/专业洞察提炼';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`专业洞察提炼记录已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 专业洞察提炼记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成专业洞察提炼记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "专业洞察提炼表单"}