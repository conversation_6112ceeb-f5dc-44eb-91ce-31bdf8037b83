{"id": "sour-bean-human-touch-form", "fields": [{"id": "date", "label": "创作日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "创作日期"}, {"id": "title", "label": "文章标题（用反差感）", "type": "text", "description": "如：《CEO的隐私焦虑：从偷看员工聊天记录到被AI告发》"}, {"id": "sceneLocation", "label": "具体场景地点", "type": "select", "options": [{"id": "client-office", "label": "客户办公室", "value": "客户办公室"}, {"id": "meeting-room", "label": "会议室", "value": "会议室"}, {"id": "coffee-shop", "label": "咖啡厅", "value": "咖啡厅"}, {"id": "phone-call", "label": "电话沟通", "value": "电话沟通"}, {"id": "industry-event", "label": "行业活动", "value": "行业活动"}, {"id": "home-office", "label": "家庭办公", "value": "家庭办公"}, {"id": "restaurant", "label": "餐厅", "value": "餐厅"}], "description": "选择故事发生的具体场景"}, {"id": "character", "label": "主要人物", "type": "text", "description": "如：某公司的CTO、客户老板、同事等"}, {"id": "keyDialogue", "label": "关键对话（原话记录）", "type": "textarea", "rows": 3, "description": "记录最关键的对话内容，要有行业黑话和真实感"}, {"id": "screenDetail", "label": "屏幕/环境细节", "type": "textarea", "rows": 2, "description": "如：电脑屏幕上打开的文件、背景标语、桌上的物品等"}, {"id": "myResponse", "label": "我的回应/内心OS", "type": "textarea", "rows": 2, "description": "你的犀利回应或内心独白"}, {"id": "industryRule", "label": "行业潜规则", "type": "textarea", "rows": 3, "description": "暴露的行业黑话、常见操作或潜规则"}, {"id": "officialSaying", "label": "官方说法", "type": "text", "description": "美其名曰的官方表述"}, {"id": "realPurpose", "label": "真实目的", "type": "text", "description": "实际上的真实目的"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "label": "荒诞案例", "type": "textarea", "rows": 3, "description": "某公司为了应付检查做的荒唐操作"}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "label": "反转人物", "type": "text", "description": "最懂合规的小人物（如前台大妈、保洁阿姨等）"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "反转行为", "type": "textarea", "rows": 2, "description": "这个小人物的某个行为，比高大上措施更有效"}, {"id": "lifeMetaphor", "label": "生活比喻", "type": "select", "options": [{"id": "cooking", "label": "做菜", "value": "做菜"}, {"id": "parenting", "label": "带孩子", "value": "带孩子"}, {"id": "gardening", "label": "种花", "value": "种花"}, {"id": "tailoring", "label": "做衣服", "value": "做衣服"}, {"id": "fishing", "label": "钓鱼", "value": "钓鱼"}, {"id": "playing-chess", "label": "下棋", "value": "下棋"}, {"id": "custom", "label": "其他比喻", "value": "custom"}], "description": "选择用来比喻合规工作的生活场景"}, {"id": "customMetaphor", "label": "自定义比喻（如选择其他）", "type": "text", "description": "当选择其他比喻时填写具体内容"}, {"id": "surfaceBehavior", "label": "表面行为", "type": "text", "description": "你以为在做什么"}, {"id": "coreTruth", "label": "内核真相", "type": "text", "description": "其实拼的是什么"}, {"id": "emotionalCore", "label": "情感内核", "type": "select", "options": [{"id": "helplessness", "label": "无奈", "value": "无奈"}, {"id": "irony", "label": "讽刺", "value": "讽刺"}, {"id": "absurdity", "label": "荒诞", "value": "荒诞"}, {"id": "anger", "label": "愤怒", "value": "愤怒"}, {"id": "sympathy", "label": "同情", "value": "同情"}, {"id": "enlightenment", "label": "顿悟", "value": "顿悟"}], "description": "选择这个故事的情感内核"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 酸豆角式表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const title = form.title || '合规江湖奇遇记';\n    const sceneLocation = form.sceneLocation || '客户办公室';\n    const character = form.character || '某公司CTO';\n    const keyDialogue = form.keyDialogue || '关键对话内容';\n    const screenDetail = form.screenDetail || '屏幕环境细节';\n    const myResponse = form.myResponse || '我的回应';\n    const industryRule = form.industryRule || '行业潜规则';\n    const officialSaying = form.officialSaying || '官方说法';\n    const realPurpose = form.realPurpose || '真实目的';\n    const absurdExample = form.absurdExample || '荒诞案例';\n    const ironicCharacter = form.ironicCharacter || '前台大妈';\n    const ironicBehavior = form.ironicBehavior || '反转行为';\n    const lifeMetaphor = form.lifeMetaphor || '做菜';\n    const customMetaphor = form.customMetaphor || '';\n    const surfaceBehavior = form.surfaceBehavior || '表面行为';\n    const coreTruth = form.coreTruth || '内核真相';\n    const emotionalCore = form.emotionalCore || '无奈';\n    \n    const finalMetaphor = lifeMetaphor === 'custom' ? (customMetaphor || '自定义比喻') : lifeMetaphor;\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\ntitle: ${title}\\nsceneLocation: ${sceneLocation}\\ncharacter: ${character}\\nlifeMetaphor: ${finalMetaphor}\\nemotionalCore: ${emotionalCore}\\ntags:\\n  - 酸豆角式创作\\n  - 人味故事\\n  - ${emotionalCore}\\n  - ${finalMetaphor}\\n  - 合规江湖\\ncreatedBy: 酸豆角式人味创作系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# ${title}\\n\\n## 📍 开篇场景（具体代入）\\n\\n那天，${character}在${sceneLocation}拍着桌子对我说：\\n> \"${keyDialogue}\"\\n\\n我看了看他电脑屏幕上打开的${screenDetail}，叹了口气：\\n> \"${myResponse}\"\\n\\n## 🌊 冲突揭露（暴露潜规则）\\n\\n其实这行有个潜规则：${industryRule}，美其名曰\"${officialSaying}\"，实际上就是${realPurpose}。\\n\\n比如${absurdExample}\\n\\n## 🔄 反转时刻（人性细节）\\n\\n最讽刺的是，${ironicCharacter}反而最懂合规——${ironicBehavior}，比那些高大上的措施有效多了。\\n\\n## 💡 收尾比喻（生活智慧）\\n\\n搞隐私合规就像${finalMetaphor}，你以为在${surfaceBehavior}，其实拼的是${coreTruth}。\\n\\n---\\n\\n**情感内核**：${emotionalCore}\\n**场景地点**：${sceneLocation}\\n**核心人物**：${character}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位擅长\"酸豆角式\"人味创作的作家。请基于以下素材进行创作：\\n\\n原始素材：\\n${baseTemplate}\\n\\n请运用酸豆角式人味创作技巧，将这个素材改造成有温度、有洞察、有人味的创作内容。要求语言有温度和质感，避免AI化表达，字数控制在600-1000字。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.9,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI酸豆角式改造暂时不可用，请手动补充人味创作)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## ✨ AI酸豆角式人味改造\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 创作应用记录\\n\\n<!-- 记录这个故事在实际创作中的使用情况 -->\\n\\n## 🔄 故事迭代优化\\n\\n<!-- 记录故事的进一步加工和优化 -->\\n\\n---\\n\\n*创作时间：${new Date().toLocaleString('zh-CN')} | AI酸豆角式改造：DeepSeek | 人味创作库*`;\n    \n    const fileName = `酸豆角式创作-${title.substring(0, 10)}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/酸豆角式创作/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/酸豆角式创作';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`酸豆角式创作已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 酸豆角式创作已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成酸豆角式创作失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "酸豆角式人味创作表单"}