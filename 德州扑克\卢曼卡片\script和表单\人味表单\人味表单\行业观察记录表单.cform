{"id": "industry-observation-form", "fields": [{"id": "date", "label": "观察日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "记录观察的日期"}, {"id": "observationTitle", "label": "观察标题", "type": "text", "description": "用一句话概括这次观察的核心"}, {"id": "industry", "label": "行业领域", "type": "select", "options": [{"id": "privacy-compliance", "label": "隐私合规", "value": "隐私合规"}, {"id": "data-security", "label": "数据安全", "value": "数据安全"}, {"id": "fintech", "label": "金融科技", "value": "金融科技"}, {"id": "ecommerce", "label": "电商平台", "value": "电商平台"}, {"id": "healthcare", "label": "医疗健康", "value": "医疗健康"}, {"id": "education", "label": "在线教育", "value": "在线教育"}, {"id": "social-media", "label": "社交媒体", "value": "社交媒体"}, {"id": "gaming", "label": "游戏娱乐", "value": "游戏娱乐"}, {"id": "other", "label": "其他行业", "value": "其他行业"}], "description": "选择观察涉及的行业领域"}, {"id": "observationScene", "label": "观察场景", "type": "select", "options": [{"id": "client-meeting", "label": "客户会议", "value": "客户会议"}, {"id": "industry-conference", "label": "行业会议", "value": "行业会议"}, {"id": "regulatory-briefing", "label": "监管通报", "value": "监管通报"}, {"id": "peer-discussion", "label": "同行交流", "value": "同行交流"}, {"id": "news-analysis", "label": "新闻分析", "value": "新闻分析"}, {"id": "case-study", "label": "案例研究", "value": "案例研究"}, {"id": "daily-work", "label": "日常工作", "value": "日常工作"}], "description": "选择观察发生的场景"}, {"id": "surfacePhenomenon", "label": "表面现象", "type": "textarea", "rows": 3, "description": "记录你观察到的表面现象或行为"}, {"id": "deepInsight", "label": "深层洞察", "type": "textarea", "rows": 3, "description": "透过现象看到的本质或潜规则"}, {"id": "keyDialogue", "label": "关键对话/金句", "type": "textarea", "rows": 2, "description": "记录关键的对话或经典语录"}, {"id": "emotionalReaction", "label": "情感反应", "type": "select", "options": [{"id": "surprised", "label": "惊讶", "value": "惊讶"}, {"id": "frustrated", "label": "无奈", "value": "无奈"}, {"id": "angry", "label": "愤怒", "value": "愤怒"}, {"id": "enlightened", "label": "顿悟", "value": "顿悟"}, {"id": "worried", "label": "担忧", "value": "担忧"}, {"id": "amused", "label": "好笑", "value": "好笑"}, {"id": "confused", "label": "困惑", "value": "困惑"}], "description": "选择你的情感反应"}, {"id": "industryBlackTalk", "label": "行业黑话/内部说法", "type": "text", "description": "记录行业内部的专业术语或黑话"}, {"id": "contradictionPoint", "label": "矛盾点", "type": "textarea", "rows": 2, "description": "官方说法与实际操作的矛盾之处"}, {"id": "practicalEvidence", "label": "实际证据", "type": "textarea", "rows": 2, "description": "支撑观察的具体证据或案例"}, {"id": "trendPrediction", "label": "趋势预测", "type": "textarea", "rows": 2, "description": "基于观察对行业趋势的预测"}, {"id": "applicationValue", "label": "应用价值", "type": "select", "options": [{"id": "article-material", "label": "文章素材", "value": "文章素材"}, {"id": "client-education", "label": "客户教育", "value": "客户教育"}, {"id": "industry-analysis", "label": "行业分析", "value": "行业分析"}, {"id": "risk-warning", "label": "风险预警", "value": "风险预警"}, {"id": "business-opportunity", "label": "商业机会", "value": "商业机会"}, {"id": "regulatory-insight", "label": "监管洞察", "value": "监管洞察"}], "description": "这个观察最适合的应用场景"}, {"id": "urgencyLevel", "label": "紧急程度", "type": "select", "options": [{"id": "immediate", "label": "立即处理", "value": "立即处理"}, {"id": "this-week", "label": "本周处理", "value": "本周处理"}, {"id": "this-month", "label": "本月处理", "value": "本月处理"}, {"id": "archive", "label": "存档备用", "value": "存档备用"}], "description": "选择处理的紧急程度"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 行业观察记录表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const observationTitle = form.observationTitle || '行业观察记录';\n    const industry = form.industry || '隐私合规';\n    const observationScene = form.observationScene || '客户会议';\n    const surfacePhenomenon = form.surfacePhenomenon || '表面现象';\n    const deepInsight = form.deepInsight || '深层洞察';\n    const keyDialogue = form.keyDialogue || '关键对话';\n    const emotionalReaction = form.emotionalReaction || '无奈';\n    const industryBlackTalk = form.industryBlackTalk || '行业黑话';\n    const contradictionPoint = form.contradictionPoint || '矛盾点';\n    const practicalEvidence = form.practicalEvidence || '实际证据';\n    const trendPrediction = form.trendPrediction || '趋势预测';\n    const applicationValue = form.applicationValue || '文章素材';\n    const urgencyLevel = form.urgencyLevel || '本周处理';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\ntitle: ${observationTitle}\\nindustry: ${industry}\\nobservationScene: ${observationScene}\\nemotionalReaction: ${emotionalReaction}\\napplicationValue: ${applicationValue}\\nurgencyLevel: ${urgencyLevel}\\ntags:\\n  - 行业观察\\n  - ${industry}\\n  - ${observationScene}\\n  - ${emotionalReaction}\\n  - ${applicationValue}\\ncreatedBy: 行业观察记录系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# ${observationTitle}\\n\\n## 📊 观察基础信息\\n\\n**观察日期：** ${date}\\n**行业领域：** ${industry}\\n**观察场景：** ${observationScene}\\n**情感反应：** ${emotionalReaction}\\n\\n## 🔍 现象与洞察\\n\\n### 表面现象\\n${surfacePhenomenon}\\n\\n### 深层洞察\\n${deepInsight}\\n\\n### 关键对话/金句\\n> \"${keyDialogue}\"\\n\\n## 🎯 行业分析\\n\\n**行业黑话/内部说法：** ${industryBlackTalk}\\n\\n**矛盾点：** ${contradictionPoint}\\n\\n**实际证据：** ${practicalEvidence}\\n\\n## 🔮 趋势预测\\n\\n${trendPrediction}\\n\\n## 💡 应用价值\\n\\n**适用场景：** ${applicationValue}\\n**处理优先级：** ${urgencyLevel}\\n\\n---\\n\\n**快速标签：** #${industry} #${observationScene} #${emotionalReaction}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的行业观察专家。请基于以下行业观察记录，提供深度的分析和洞察：\\n\\n观察记录：\\n${baseTemplate}\\n\\n请从以下角度进行分析：\\n1. 观察的深层价值和意义\\n2. 与行业趋势的关联性\\n3. 潜在的商业机会或风险\\n4. 可操作的应用建议\\n5. 与其他行业现象的连接点\\n\\n要求语言专业但有人味，避免空洞的分析。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI深度分析暂时不可用，请手动补充专业洞察)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## 🧠 AI深度分析与洞察\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 后续跟踪记录\\n\\n<!-- 记录这个观察的后续发展和验证 -->\\n\\n## 🔄 观察迭代优化\\n\\n<!-- 记录观察的进一步深化和拓展 -->\\n\\n---\\n\\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI深度分析：DeepSeek | 行业观察库*`;\n    \n    const fileName = `行业观察-${industry}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/行业观察记录/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/行业观察记录';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`行业观察记录已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 行业观察记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成行业观察记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "行业观察记录表单"}