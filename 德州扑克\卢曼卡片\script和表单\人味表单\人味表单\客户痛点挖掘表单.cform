{"id": "client-pain-point-form", "fields": [{"id": "date", "label": "记录日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "痛点记录日期"}, {"id": "clientProfile", "label": "客户画像", "type": "select", "options": [{"id": "startup-ceo", "label": "创业公司CEO", "value": "创业公司CEO"}, {"id": "enterprise-cto", "label": "大企业CTO", "value": "大企业CTO"}, {"id": "compliance-manager", "label": "合规经理", "value": "合规经理"}, {"id": "legal-counsel", "label": "法务顾问", "value": "法务顾问"}, {"id": "product-manager", "label": "产品经理", "value": "产品经理"}, {"id": "data-engineer", "label": "数据工程师", "value": "数据工程师"}, {"id": "business-owner", "label": "业务负责人", "value": "业务负责人"}], "description": "选择客户的角色类型"}, {"id": "companySize", "label": "公司规模", "type": "select", "options": [{"id": "startup", "label": "初创公司（<50人）", "value": "初创公司"}, {"id": "small", "label": "小型公司（50-200人）", "value": "小型公司"}, {"id": "medium", "label": "中型公司（200-1000人）", "value": "中型公司"}, {"id": "large", "label": "大型公司（1000+人）", "value": "大型公司"}, {"id": "multinational", "label": "跨国公司", "value": "跨国公司"}], "description": "选择公司规模"}, {"id": "painPointType", "label": "痛点类型", "type": "select", "options": [{"id": "cost-concern", "label": "成本担忧", "value": "成本担忧"}, {"id": "technical-difficulty", "label": "技术难度", "value": "技术难度"}, {"id": "business-impact", "label": "业务影响", "value": "业务影响"}, {"id": "regulatory-confusion", "label": "监管困惑", "value": "监管困惑"}, {"id": "resource-shortage", "label": "资源不足", "value": "资源不足"}, {"id": "time-pressure", "label": "时间压力", "value": "时间压力"}, {"id": "knowledge-gap", "label": "知识缺口", "value": "知识缺口"}], "description": "选择主要的痛点类型"}, {"id": "clientOriginalWords", "label": "客户原话", "type": "textarea", "rows": 3, "description": "记录客户的原始表达，保持真实性"}, {"id": "hiddenConcern", "label": "隐藏担忧", "type": "textarea", "rows": 3, "description": "客户没有直接说出但实际担心的问题"}, {"id": "realNeed", "label": "真实需求", "type": "textarea", "rows": 2, "description": "透过表面需求看到的真实需求"}, {"id": "emotionalState", "label": "情感状态", "type": "select", "options": [{"id": "anxious", "label": "焦虑", "value": "焦虑"}, {"id": "frustrated", "label": "沮丧", "value": "沮丧"}, {"id": "confused", "label": "困惑", "value": "困惑"}, {"id": "angry", "label": "愤怒", "value": "愤怒"}, {"id": "helpless", "label": "无助", "value": "无助"}, {"id": "skeptical", "label": "怀疑", "value": "怀疑"}, {"id": "urgent", "label": "急迫", "value": "急迫"}], "description": "客户的情感状态"}, {"id": "bodyLanguage", "label": "肢体语言/细节", "type": "textarea", "rows": 2, "description": "观察到的肢体语言或环境细节"}, {"id": "industryContext", "label": "行业背景", "type": "textarea", "rows": 2, "description": "这个痛点在该行业的普遍性和特殊性"}, {"id": "myResponse", "label": "我的回应策略", "type": "textarea", "rows": 3, "description": "你采用的回应策略和具体话术"}, {"id": "clientReaction", "label": "客户反应", "type": "select", "options": [{"id": "relieved", "label": "如释重负", "value": "如释重负"}, {"id": "still-worried", "label": "仍然担忧", "value": "仍然担忧"}, {"id": "more-confused", "label": "更加困惑", "value": "更加困惑"}, {"id": "resistant", "label": "抗拒", "value": "抗拒"}, {"id": "interested", "label": "感兴趣", "value": "感兴趣"}, {"id": "skeptical", "label": "怀疑", "value": "怀疑"}, {"id": "convinced", "label": "被说服", "value": "被说服"}], "description": "客户对你回应的反应"}, {"id": "solutionDirection", "label": "解决方向", "type": "textarea", "rows": 2, "description": "针对这个痛点的解决方向或产品机会"}, {"id": "marketInsight", "label": "市场洞察", "type": "textarea", "rows": 2, "description": "从这个痛点看到的市场机会或趋势"}, {"id": "contentValue", "label": "内容价值", "type": "select", "options": [{"id": "case-study", "label": "案例研究", "value": "案例研究"}, {"id": "product-insight", "label": "产品洞察", "value": "产品洞察"}, {"id": "market-analysis", "label": "市场分析", "value": "市场分析"}, {"id": "client-education", "label": "客户教育", "value": "客户教育"}, {"id": "industry-report", "label": "行业报告", "value": "行业报告"}, {"id": "solution-design", "label": "解决方案设计", "value": "解决方案设计"}], "description": "这个痛点记录的内容价值"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 客户痛点挖掘表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const clientProfile = form.clientProfile || '创业公司CEO';\n    const companySize = form.companySize || '初创公司';\n    const painPointType = form.painPointType || '成本担忧';\n    const clientOriginalWords = form.clientOriginalWords || '客户原话';\n    const hiddenConcern = form.hiddenConcern || '隐藏担忧';\n    const realNeed = form.realNeed || '真实需求';\n    const emotionalState = form.emotionalState || '焦虑';\n    const bodyLanguage = form.bodyLanguage || '肢体语言细节';\n    const industryContext = form.industryContext || '行业背景';\n    const myResponse = form.myResponse || '我的回应策略';\n    const clientReaction = form.clientReaction || '如释重负';\n    const solutionDirection = form.solutionDirection || '解决方向';\n    const marketInsight = form.marketInsight || '市场洞察';\n    const contentValue = form.contentValue || '案例研究';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\nclientProfile: ${clientProfile}\\ncompanySize: ${companySize}\\npainPointType: ${painPointType}\\nemotionalState: ${emotionalState}\\nclientReaction: ${clientReaction}\\ncontentValue: ${contentValue}\\ntags:\\n  - 客户痛点挖掘\\n  - ${clientProfile}\\n  - ${companySize}\\n  - ${painPointType}\\n  - ${emotionalState}\\ncreatedBy: 客户痛点挖掘系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# 客户痛点挖掘记录\\n\\n## 👤 客户基础信息\\n\\n**记录日期：** ${date}\\n**客户画像：** ${clientProfile}\\n**公司规模：** ${companySize}\\n**痛点类型：** ${painPointType}\\n**情感状态：** ${emotionalState}\\n\\n## 💬 客户表达\\n\\n### 客户原话\\n> \"${clientOriginalWords}\"\\n\\n### 肢体语言/环境细节\\n${bodyLanguage}\\n\\n## 🔍 深度分析\\n\\n### 隐藏担忧\\n${hiddenConcern}\\n\\n### 真实需求\\n${realNeed}\\n\\n### 行业背景\\n${industryContext}\\n\\n## 🎯 应对策略\\n\\n### 我的回应策略\\n${myResponse}\\n\\n### 客户反应\\n**反应类型：** ${clientReaction}\\n\\n## 💡 洞察与机会\\n\\n### 解决方向\\n${solutionDirection}\\n\\n### 市场洞察\\n${marketInsight}\\n\\n### 内容价值\\n**适用场景：** ${contentValue}\\n\\n---\\n\\n**快速标签：** #${clientProfile} #${painPointType} #${emotionalState}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的客户洞察专家。请基于以下客户痛点记录，提供深度的分析和建议：\\n\\n痛点记录：\\n${baseTemplate}\\n\\n请从以下角度进行分析：\\n1. 痛点的深层心理分析\\n2. 同类客户的共性问题\\n3. 解决方案的设计思路\\n4. 产品或服务的改进建议\\n5. 市场机会的挖掘\\n6. 客户教育的策略\\n\\n要求语言专业但有温度，体现对客户的深度理解。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.8,\n          max_tokens: 2000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI深度分析暂时不可用，请手动补充客户洞察)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## 🧠 AI深度客户洞察\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 后续跟踪记录\\n\\n<!-- 记录与该客户的后续互动和痛点变化 -->\\n\\n## 🔄 解决方案迭代\\n\\n<!-- 记录针对此类痛点的解决方案优化 -->\\n\\n---\\n\\n*记录时间：${new Date().toLocaleString('zh-CN')} | AI客户洞察：DeepSeek | 客户痛点库*`;\n    \n    const fileName = `客户痛点-${clientProfile}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/客户痛点挖掘/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/客户痛点挖掘';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`客户痛点挖掘记录已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 客户痛点挖掘记录已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成客户痛点挖掘记录失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "客户痛点挖掘表单"}