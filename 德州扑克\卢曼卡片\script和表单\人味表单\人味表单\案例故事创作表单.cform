{"id": "case-story-creation-form", "fields": [{"id": "date", "label": "创作日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "案例故事创作日期"}, {"id": "storyTitle", "label": "故事标题", "type": "text", "description": "用吸引人的标题概括这个案例故事"}, {"id": "storyType", "label": "故事类型", "type": "select", "options": [{"id": "success-case", "label": "成功案例", "value": "成功案例"}, {"id": "failure-lesson", "label": "失败教训", "value": "失败教训"}, {"id": "transformation", "label": "转型故事", "value": "转型故事"}, {"id": "crisis-response", "label": "危机应对", "value": "危机应对"}, {"id": "innovation-breakthrough", "label": "创新突破", "value": "创新突破"}, {"id": "compliance-journey", "label": "合规历程", "value": "合规历程"}, {"id": "industry-insight", "label": "行业洞察", "value": "行业洞察"}], "description": "选择故事的类型"}, {"id": "protagonist", "label": "主角设定", "type": "text", "description": "故事的主角（可以是公司、个人或团队）"}, {"id": "backgroundSetting", "label": "背景设定", "type": "textarea", "rows": 3, "description": "故事发生的时间、地点、行业背景"}, {"id": "initialChallenge", "label": "初始挑战", "type": "textarea", "rows": 3, "description": "主角面临的核心挑战或问题"}, {"id": "stakeholders", "label": "利益相关者", "type": "textarea", "rows": 2, "description": "故事中的其他重要角色和他们的立场"}, {"id": "conflictPoint", "label": "冲突点", "type": "textarea", "rows": 2, "description": "故事中的主要冲突或转折点"}, {"id": "decisionMoment", "label": "关键决策时刻", "type": "textarea", "rows": 3, "description": "主角做出关键决策的时刻和考虑因素"}, {"id": "actionTaken", "label": "采取的行动", "type": "textarea", "rows": 3, "description": "主角具体采取的行动和实施过程"}, {"id": "obstacles", "label": "遇到的障碍", "type": "textarea", "rows": 2, "description": "实施过程中遇到的困难和阻力"}, {"id": "turningPoint", "label": "转折点", "type": "textarea", "rows": 2, "description": "故事的关键转折点或突破时刻"}, {"id": "finalOutcome", "label": "最终结果", "type": "textarea", "rows": 3, "description": "故事的最终结果和影响"}, {"id": "lessonsLearned", "label": "经验教训", "type": "textarea", "rows": 3, "description": "从这个故事中得到的关键经验教训"}, {"id": "emotionalArc", "label": "情感弧线", "type": "select", "options": [{"id": "triumph", "label": "胜利", "value": "胜利"}, {"id": "redemption", "label": "救赎", "value": "救赎"}, {"id": "tragedy", "label": "悲剧", "value": "悲剧"}, {"id": "growth", "label": "成长", "value": "成长"}, {"id": "discovery", "label": "发现", "value": "发现"}, {"id": "sacrifice", "label": "牺牲", "value": "牺牲"}, {"id": "transformation", "label": "蜕变", "value": "蜕变"}], "description": "故事的情感主线"}, {"id": "universalTheme", "label": "普遍主题", "type": "textarea", "rows": 2, "description": "这个故事反映的普遍性主题或人性洞察"}, {"id": "industryRelevance", "label": "行业相关性", "type": "textarea", "rows": 2, "description": "这个故事对行业的启发和借鉴意义"}, {"id": "targetAudience", "label": "目标受众", "type": "select", "options": [{"id": "executives", "label": "企业高管", "value": "企业高管"}, {"id": "compliance-professionals", "label": "合规专业人士", "value": "合规专业人士"}, {"id": "legal-practitioners", "label": "法律从业者", "value": "法律从业者"}, {"id": "entrepreneurs", "label": "创业者", "value": "创业者"}, {"id": "industry-peers", "label": "行业同行", "value": "行业同行"}, {"id": "general-public", "label": "普通大众", "value": "普通大众"}, {"id": "students", "label": "学生群体", "value": "学生群体"}], "description": "这个故事的主要目标受众"}, {"id": "narrativeStyle", "label": "叙述风格", "type": "select", "options": [{"id": "documentary", "label": "纪实风格", "value": "纪实风格"}, {"id": "dramatic", "label": "戏剧化", "value": "戏剧化"}, {"id": "analytical", "label": "分析性", "value": "分析性"}, {"id": "conversational", "label": "对话式", "value": "对话式"}, {"id": "suspenseful", "label": "悬疑式", "value": "悬疑式"}, {"id": "inspirational", "label": "励志式", "value": "励志式"}, {"id": "humorous", "label": "幽默式", "value": "幽默式"}], "description": "选择故事的叙述风格"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 案例故事创作表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const storyTitle = form.storyTitle || '案例故事';\n    const storyType = form.storyType || '成功案例';\n    const protagonist = form.protagonist || '主角';\n    const backgroundSetting = form.backgroundSetting || '背景设定';\n    const initialChallenge = form.initialChallenge || '初始挑战';\n    const stakeholders = form.stakeholders || '利益相关者';\n    const conflictPoint = form.conflictPoint || '冲突点';\n    const decisionMoment = form.decisionMoment || '关键决策时刻';\n    const actionTaken = form.actionTaken || '采取的行动';\n    const obstacles = form.obstacles || '遇到的障碍';\n    const turningPoint = form.turningPoint || '转折点';\n    const finalOutcome = form.finalOutcome || '最终结果';\n    const lessonsLearned = form.lessonsLearned || '经验教训';\n    const emotionalArc = form.emotionalArc || '成长';\n    const universalTheme = form.universalTheme || '普遍主题';\n    const industryRelevance = form.industryRelevance || '行业相关性';\n    const targetAudience = form.targetAudience || '企业高管';\n    const narrativeStyle = form.narrativeStyle || '纪实风格';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\ntitle: ${storyTitle}\\nstoryType: ${storyType}\\nprotagonist: ${protagonist}\\nemotionalArc: ${emotionalArc}\\ntargetAudience: ${targetAudience}\\nnarrativeStyle: ${narrativeStyle}\\ntags:\\n  - 案例故事创作\\n  - ${storyType}\\n  - ${emotionalArc}\\n  - ${targetAudience}\\n  - ${narrativeStyle}\\ncreatedBy: 案例故事创作系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# ${storyTitle}\\n\\n## 📋 故事基础信息\\n\\n**创作日期：** ${date}\\n**故事类型：** ${storyType}\\n**主角：** ${protagonist}\\n**情感弧线：** ${emotionalArc}\\n**目标受众：** ${targetAudience}\\n**叙述风格：** ${narrativeStyle}\\n\\n## 🎬 故事结构\\n\\n### 背景设定\\n${backgroundSetting}\\n\\n### 初始挑战\\n${initialChallenge}\\n\\n### 利益相关者\\n${stakeholders}\\n\\n## ⚡ 冲突与发展\\n\\n### 冲突点\\n${conflictPoint}\\n\\n### 关键决策时刻\\n${decisionMoment}\\n\\n### 采取的行动\\n${actionTaken}\\n\\n### 遇到的障碍\\n${obstacles}\\n\\n## 🔄 转折与结局\\n\\n### 转折点\\n${turningPoint}\\n\\n### 最终结果\\n${finalOutcome}\\n\\n## 💡 洞察与价值\\n\\n### 经验教训\\n${lessonsLearned}\\n\\n### 普遍主题\\n${universalTheme}\\n\\n### 行业相关性\\n${industryRelevance}\\n\\n---\\n\\n**快速标签：** #${storyType} #${emotionalArc} #${targetAudience}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的故事创作专家。请基于以下案例故事框架，创作一个完整的、有人味的故事：\\n\\n故事框架：\\n${baseTemplate}\\n\\n请运用${narrativeStyle}的叙述风格，为${targetAudience}创作一个完整的故事。要求：\\n1. 故事要有真实感和代入感\\n2. 人物形象要立体生动\\n3. 情节发展要符合${emotionalArc}的情感弧线\\n4. 语言要有温度和质感\\n5. 结尾要有启发性和思考价值\\n\\n字数控制在1000-1500字。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.9,\n          max_tokens: 3000\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI故事创作暂时不可用，请手动补充完整故事)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## ✨ AI完整故事创作\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 故事应用记录\\n\\n<!-- 记录这个故事在实际场景中的使用效果 -->\\n\\n## 🔄 故事迭代优化\\n\\n<!-- 记录故事的进一步完善和改进 -->\\n\\n---\\n\\n*创作时间：${new Date().toLocaleString('zh-CN')} | AI故事创作：DeepSeek | 案例故事库*`;\n    \n    const fileName = `案例故事-${storyType}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/案例故事创作/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/案例故事创作';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`案例故事创作已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 案例故事创作已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成案例故事创作失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "案例故事创作表单"}