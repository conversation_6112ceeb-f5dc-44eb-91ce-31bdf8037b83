/**
 * 高级人味模板提取脚本
 * 功能：批量提取并分类整理人味模板，生成索引文件
 */

const fs = require('fs');
const path = require('path');

async function advancedExtractHumanTemplates() {
    
    try {
        // 配置
        const sourceFolder = path.join(__dirname, '人味模板');
        const targetBaseFolder = path.join(__dirname, '..', '..', '..', '工作室', '肌肉', '人味模板库');
        
        console.log('开始高级人味模板提取...');
        
        // 获取源文件夹
        if (!fs.existsSync(sourceFolder)) {
            console.error('源文件夹不存在: ' + sourceFolder);
            return;
        }
        
        // 创建目标文件夹结构
        const folders = [
            targetBaseFolder,
            `${targetBaseFolder}/核心模板`,
            `${targetBaseFolder}/写作技巧`,
            `${targetBaseFolder}/案例分析`,
            `${targetBaseFolder}/行业观察`,
            `${targetBaseFolder}/其他模板`
        ];
        
        for (const folder of folders) {
            if (!fs.existsSync(folder)) {
                fs.mkdirSync(folder, { recursive: true });
                console.log('已创建文件夹: ' + folder);
            }
        }
        
        // 获取所有模板文件
        const files = fs.readdirSync(sourceFolder)
            .filter(file => file.endsWith('.md'))
            .map(file => ({
                name: file,
                path: path.join(sourceFolder, file)
            }));
        
        console.log(`找到 ${files.length} 个模板文件`);
        
        // 模板分类规则
        const classifyTemplate = (fileName, content) => {
            const name = fileName.toLowerCase();
            const contentLower = content.toLowerCase();
            
            // 核心模板
            if (name.includes('人味—') || name.includes('酸豆角') || 
                name.includes('素材记录模板') || name.includes('写作行动清单')) {
                return '核心模板';
            }
            
            // 写作技巧
            if (name.includes('写作') || name.includes('技巧') || name.includes('方法') ||
                contentLower.includes('写作技巧') || contentLower.includes('表达方式')) {
                return '写作技巧';
            }
            
            // 案例分析
            if (name.includes('案例') || name.includes('公司') || name.includes('评估') ||
                contentLower.includes('案例') || contentLower.includes('分析')) {
                return '案例分析';
            }
            
            // 行业观察
            if (name.includes('观察') || name.includes('本质') || name.includes('规则') ||
                contentLower.includes('行业') || contentLower.includes('监管')) {
                return '行业观察';
            }
            
            return '其他模板';
        };
        
        let successCount = 0;
        let errorCount = 0;
        const templateIndex = [];
        
        // 批量处理文件
        for (const file of files) {
            try {
                // 读取文件内容
                const content = fs.readFileSync(file.path, 'utf-8');
                
                // 分类模板
                const category = classifyTemplate(file.name, content);
                
                // 生成新文件路径
                const newFileName = file.name;
                const newFilePath = `${targetBaseFolder}/${category}/${newFileName}`;
                
                // 检查是否已存在
                if (fs.existsSync(newFilePath)) {
                    console.log(`文件已存在，跳过: ${newFileName}`);
                    continue;
                }
                
                fs.writeFileSync(newFilePath, content);
                console.log(`✅ 已复制到 [${category}]: ${newFileName}`);
                
                // 添加到索引
                templateIndex.push({
                    name: newFileName,
                    category: category,
                    path: newFilePath,
                    size: content.length,
                    preview: content.substring(0, 100).replace(/\n/g, ' ')
                });
                
                successCount++;
                
            } catch (error) {
                console.error(`❌ 处理失败: ${file.name}`, error);
                errorCount++;
            }
        }
        
        // 生成索引文件
        try {
            const indexContent = generateIndexContent(templateIndex);
            const indexPath = `${targetBaseFolder}/人味模板索引.md`;
            await app.vault.create(indexPath, indexContent);
            console.log('✅ 已生成索引文件');
        } catch (error) {
            console.error('生成索引文件失败:', error);
        }
        
        // 生成统计报告
        const stats = generateStats(templateIndex);
        const statsPath = `${targetBaseFolder}/提取统计报告.md`;
        await app.vault.create(statsPath, stats);
        
        // 显示结果
        const message = `高级提取完成！\n✅ 成功: ${successCount} 个\n❌ 失败: ${errorCount} 个\n📁 目标位置: ${targetBaseFolder}`;
        new Notice(message);
        console.log(message);
        
        // 打开目标文件夹
        console.log(`提取完成，文件保存在: ${targetBaseFolder}`);
        
        return message;
        
    } catch (error) {
        console.error('高级提取失败:', error);
        console.error('高级提取失败: ' + error.message);
        return '❌ 高级提取失败';
    }
}

// 生成索引内容
function generateIndexContent(templateIndex) {
    const today = new Date().toISOString().split('T')[0];
    
    let content = `# 人味模板索引\n\n> 生成时间：${today}\n> 总计模板：${templateIndex.length} 个\n\n`;
    
    // 按分类组织
    const categories = [...new Set(templateIndex.map(t => t.category))];
    
    for (const category of categories) {
        const templates = templateIndex.filter(t => t.category === category);
        content += `## ${category} (${templates.length}个)\n\n`;
        
        for (const template of templates) {
            content += `### [[${template.path}|${template.name}]]\n`;
            content += `- **大小**: ${template.size} 字符\n`;
            content += `- **预览**: ${template.preview}...\n\n`;
        }
    }
    
    content += `\n---\n\n## 使用说明\n\n1. 点击模板名称可直接打开对应文件\n2. 模板已按类别自动分类\n3. 可根据需要进一步整理和修改\n\n## 分类说明\n\n- **核心模板**: 最重要的人味创作模板\n- **写作技巧**: 写作方法和技巧相关\n- **案例分析**: 具体案例和分析模板\n- **行业观察**: 行业洞察和观察模板\n- **其他模板**: 其他类型的模板\n\n*自动生成于 ${new Date().toLocaleString()}*`;
    
    return content;
}

// 生成统计报告
function generateStats(templateIndex) {
    const today = new Date().toISOString().split('T')[0];
    const categories = [...new Set(templateIndex.map(t => t.category))];
    
    let content = `# 人味模板提取统计报告\n\n> 提取时间：${today}\n\n## 📊 总体统计\n\n`;
    content += `- **总模板数**: ${templateIndex.length} 个\n`;
    content += `- **总字符数**: ${templateIndex.reduce((sum, t) => sum + t.size, 0).toLocaleString()} 字符\n`;
    content += `- **平均大小**: ${Math.round(templateIndex.reduce((sum, t) => sum + t.size, 0) / templateIndex.length).toLocaleString()} 字符\n\n`;
    
    content += `## 📁 分类统计\n\n`;
    for (const category of categories) {
        const templates = templateIndex.filter(t => t.category === category);
        const totalSize = templates.reduce((sum, t) => sum + t.size, 0);
        content += `### ${category}\n`;
        content += `- 数量: ${templates.length} 个\n`;
        content += `- 总大小: ${totalSize.toLocaleString()} 字符\n`;
        content += `- 平均大小: ${Math.round(totalSize / templates.length).toLocaleString()} 字符\n\n`;
    }
    
    content += `## 📋 详细清单\n\n`;
    for (const template of templateIndex.sort((a, b) => b.size - a.size)) {
        content += `- **${template.name}** (${template.category}) - ${template.size.toLocaleString()} 字符\n`;
    }
    
    content += `\n---\n*报告生成于 ${new Date().toLocaleString()}*`;
    
    return content;
}

// 执行脚本
advancedExtractHumanTemplates();
