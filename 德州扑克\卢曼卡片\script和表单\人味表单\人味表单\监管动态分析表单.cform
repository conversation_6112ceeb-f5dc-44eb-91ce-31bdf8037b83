{"id": "regulatory-dynamics-form", "fields": [{"id": "date", "label": "分析日期", "type": "text", "defaultValue": "{{date:YYYY-MM-DD}}", "description": "监管动态分析日期"}, {"id": "regulatoryTitle", "label": "监管事件标题", "type": "text", "description": "用简洁的标题概括监管事件"}, {"id": "regulatoryType", "label": "监管类型", "type": "select", "options": [{"id": "new-regulation", "label": "新法规发布", "value": "新法规发布"}, {"id": "policy-interpretation", "label": "政策解读", "value": "政策解读"}, {"id": "enforcement-case", "label": "执法案例", "value": "执法案例"}, {"id": "penalty-announcement", "label": "处罚公告", "value": "处罚公告"}, {"id": "industry-guidance", "label": "行业指导", "value": "行业指导"}, {"id": "international-trend", "label": "国际趋势", "value": "国际趋势"}, {"id": "regulatory-consultation", "label": "监管征询", "value": "监管征询"}], "description": "选择监管动态的类型"}, {"id": "jurisdiction", "label": "管辖区域", "type": "select", "options": [{"id": "china", "label": "中国", "value": "中国"}, {"id": "eu", "label": "欧盟", "value": "欧盟"}, {"id": "us", "label": "美国", "value": "美国"}, {"id": "uk", "label": "英国", "value": "英国"}, {"id": "singapore", "label": "新加坡", "value": "新加坡"}, {"id": "japan", "label": "日本", "value": "日本"}, {"id": "south-korea", "label": "韩国", "value": "韩国"}, {"id": "global", "label": "全球", "value": "全球"}], "description": "选择相关的管辖区域"}, {"id": "officialStatement", "label": "官方表述", "type": "textarea", "rows": 3, "description": "记录官方的正式表述或公告内容"}, {"id": "realImpact", "label": "实际影响", "type": "textarea", "rows": 3, "description": "分析对企业和行业的实际影响"}, {"id": "hiddenMessage", "label": "隐含信息", "type": "textarea", "rows": 2, "description": "官方表述背后的隐含信息或真实意图"}, {"id": "industryReaction", "label": "行业反应", "type": "select", "options": [{"id": "panic", "label": "恐慌", "value": "恐慌"}, {"id": "confusion", "label": "困惑", "value": "困惑"}, {"id": "resistance", "label": "抗拒", "value": "抗拒"}, {"id": "acceptance", "label": "接受", "value": "接受"}, {"id": "opportunistic", "label": "机会主义", "value": "机会主义"}, {"id": "wait-and-see", "label": "观望", "value": "观望"}, {"id": "proactive", "label": "主动应对", "value": "主动应对"}], "description": "行业的主要反应类型"}, {"id": "complianceGap", "label": "合规缺口", "type": "textarea", "rows": 3, "description": "识别出的主要合规缺口和风险点"}, {"id": "businessOpportunity", "label": "商业机会", "type": "textarea", "rows": 2, "description": "从监管变化中看到的商业机会"}, {"id": "implementationChallenge", "label": "实施挑战", "type": "textarea", "rows": 2, "description": "企业在实施合规时面临的主要挑战"}, {"id": "timelineExpectation", "label": "时间线预期", "type": "select", "options": [{"id": "immediate", "label": "立即生效", "value": "立即生效"}, {"id": "within-month", "label": "一个月内", "value": "一个月内"}, {"id": "within-quarter", "label": "一个季度内", "value": "一个季度内"}, {"id": "within-year", "label": "一年内", "value": "一年内"}, {"id": "gradual", "label": "逐步实施", "value": "逐步实施"}, {"id": "unclear", "label": "时间不明", "value": "时间不明"}], "description": "预期的实施时间线"}, {"id": "similarCases", "label": "类似案例", "type": "text", "description": "历史上类似的监管案例或先例"}, {"id": "expertOpinion", "label": "专家观点", "type": "textarea", "rows": 2, "description": "行业专家或法律专家的观点"}, {"id": "actionableAdvice", "label": "可执行建议", "type": "textarea", "rows": 3, "description": "给企业的具体可执行建议"}, {"id": "monitoringPoints", "label": "监控要点", "type": "textarea", "rows": 2, "description": "需要持续监控的关键点"}, {"id": "contentApplication", "label": "内容应用", "type": "select", "options": [{"id": "client-alert", "label": "客户预警", "value": "客户预警"}, {"id": "industry-analysis", "label": "行业分析", "value": "行业分析"}, {"id": "compliance-guide", "label": "合规指南", "value": "合规指南"}, {"id": "business-strategy", "label": "商业策略", "value": "商业策略"}, {"id": "risk-assessment", "label": "风险评估", "value": "风险评估"}, {"id": "market-report", "label": "市场报告", "value": "市场报告"}], "description": "这个分析最适合的应用场景"}], "action": {"type": "runScript", "scriptSource": "inline", "code": "async function entry() {\n  const { form, app, requestUrl, Notice } = this.$context;\n  \n  try {\n    console.log('=== 监管动态分析表单调试 ===');\n    console.log('form对象:', form);\n    \n    const date = form.date || new Date().toISOString().split('T')[0];\n    const regulatoryTitle = form.regulatoryTitle || '监管动态分析';\n    const regulatoryType = form.regulatoryType || '新法规发布';\n    const jurisdiction = form.jurisdiction || '中国';\n    const officialStatement = form.officialStatement || '官方表述';\n    const realImpact = form.realImpact || '实际影响';\n    const hiddenMessage = form.hiddenMessage || '隐含信息';\n    const industryReaction = form.industryReaction || '困惑';\n    const complianceGap = form.complianceGap || '合规缺口';\n    const businessOpportunity = form.businessOpportunity || '商业机会';\n    const implementationChallenge = form.implementationChallenge || '实施挑战';\n    const timelineExpectation = form.timelineExpectation || '一个季度内';\n    const similarCases = form.similarCases || '类似案例';\n    const expertOpinion = form.expertOpinion || '专家观点';\n    const actionableAdvice = form.actionableAdvice || '可执行建议';\n    const monitoringPoints = form.monitoringPoints || '监控要点';\n    const contentApplication = form.contentApplication || '客户预警';\n    \n    const today = new Date();\n    const year = today.getFullYear();\n    const month = (today.getMonth() + 1).toString().padStart(2, '0');\n    const day = today.getDate().toString().padStart(2, '0');\n    const dateStr = `${year}${month}${day}`;\n    const timeStr = today.getHours().toString().padStart(2, '0') + today.getMinutes().toString().padStart(2, '0');\n    \n    const yamlFrontmatter = `---\\ndate: ${dateStr}\\ntitle: ${regulatoryTitle}\\nregulatoryType: ${regulatoryType}\\njurisdiction: ${jurisdiction}\\nindustryReaction: ${industryReaction}\\ntimelineExpectation: ${timelineExpectation}\\ncontentApplication: ${contentApplication}\\ntags:\\n  - 监管动态分析\\n  - ${regulatoryType}\\n  - ${jurisdiction}\\n  - ${industryReaction}\\n  - ${contentApplication}\\ncreatedBy: 监管动态分析系统\\naiModel: DeepSeek\\n---`;\n    \n    const baseTemplate = `# ${regulatoryTitle}\\n\\n## 📋 监管基础信息\\n\\n**分析日期：** ${date}\\n**监管类型：** ${regulatoryType}\\n**管辖区域：** ${jurisdiction}\\n**行业反应：** ${industryReaction}\\n**实施时间线：** ${timelineExpectation}\\n\\n## 📄 官方信息\\n\\n### 官方表述\\n${officialStatement}\\n\\n### 隐含信息\\n${hiddenMessage}\\n\\n## 🎯 影响分析\\n\\n### 实际影响\\n${realImpact}\\n\\n### 合规缺口\\n${complianceGap}\\n\\n### 实施挑战\\n${implementationChallenge}\\n\\n## 💡 机会与建议\\n\\n### 商业机会\\n${businessOpportunity}\\n\\n### 可执行建议\\n${actionableAdvice}\\n\\n## 🔍 深度洞察\\n\\n### 类似案例\\n${similarCases}\\n\\n### 专家观点\\n${expertOpinion}\\n\\n### 监控要点\\n${monitoringPoints}\\n\\n## 📊 应用价值\\n\\n**适用场景：** ${contentApplication}\\n\\n---\\n\\n**快速标签：** #${regulatoryType} #${jurisdiction} #${industryReaction}`;\n\n    let aiEnhancedContent = '';\n    const apiKey = 'sk-b37b6de26d854175a1db8d6dfe1ea0ec';\n    \n    const enhancedPrompt = `你是一位资深的监管政策分析专家。请基于以下监管动态分析，提供深度的专业洞察：\\n\\n监管分析：\\n${baseTemplate}\\n\\n请从以下角度进行深度分析：\\n1. 监管背景和政策意图的深层解读\\n2. 对不同规模企业的差异化影响\\n3. 与国际监管趋势的对比分析\\n4. 潜在的执法重点和风险预警\\n5. 行业生态的长期变化预测\\n6. 具体的合规实施路径建议\\n\\n要求语言专业严谨，体现监管政策的前瞻性分析。`;\n    \n    try {\n      const aiResponse = await requestUrl({\n        url: 'https://api.deepseek.com/v1/chat/completions',\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          model: 'deepseek-chat',\n          messages: [{ role: 'user', content: enhancedPrompt }],\n          temperature: 0.7,\n          max_tokens: 2500\n        })\n      });\n      \n      if (aiResponse.json && aiResponse.json.choices && aiResponse.json.choices[0]) {\n        aiEnhancedContent = aiResponse.json.choices[0].message.content;\n      }\n    } catch (e) {\n      aiEnhancedContent = '(AI深度分析暂时不可用，请手动补充监管洞察)';\n    }\n\n    const fullTemplate = `${yamlFrontmatter}\\n\\n${baseTemplate}\\n\\n---\\n\\n## 🧠 AI深度监管洞察\\n\\n${aiEnhancedContent}\\n\\n---\\n\\n## 📝 后续跟踪记录\\n\\n<!-- 记录监管政策的后续发展和实施情况 -->\\n\\n## 🔄 分析更新迭代\\n\\n<!-- 记录分析的更新和修正 -->\\n\\n---\\n\\n*分析时间：${new Date().toLocaleString('zh-CN')} | AI监管洞察：DeepSeek | 监管动态库*`;\n    \n    const fileName = `监管动态-${regulatoryType}-${jurisdiction}-${dateStr}-${timeStr}.md`;\n    const filePath = `工作室/肌肉/生成笔记/监管动态分析/${fileName}`;\n    \n    const folderPath = '工作室/肌肉/生成笔记/监管动态分析';\n    const folder = app.vault.getAbstractFileByPath(folderPath);\n    if (!folder) {\n      await app.vault.createFolder(folderPath);\n    }\n    \n    const file = await app.vault.create(filePath, fullTemplate);\n    new Notice(`监管动态分析已创建: ${fileName}`);\n    \n    const leaf = app.workspace.getLeaf();\n    await leaf.openFile(file);\n    \n    return `✅ 监管动态分析已生成: ${fileName}`;\n    \n  } catch (error) {\n    console.error('生成监管动态分析失败:', error);\n    new Notice('生成失败: ' + error.message);\n    return `❌ 生成失败: ${error.message}`;\n  }\n}\n\nexports.default = {\n  entry: entry\n};"}, "title": "监管动态分析表单"}